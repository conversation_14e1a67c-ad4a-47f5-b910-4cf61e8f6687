import { animate, state, style, transition, trigger } from '@angular/animations';

export const slideInOutLeftAnimation = trigger('slideInOut', [
  state(
    'in',
    style({
      width: '*',
      opacity: 1,
      transform: 'scale(1)',
      marginLeft: '12px',
    })
  ),
  state(
    'out',
    style({
      width: '0px',
      opacity: 0,
      transform: 'scale(0.95)',
      marginLeft: '0px',
    })
  ),
  state(
    'initial-in',
    style({
      width: '*',
      opacity: 1,
      transform: 'scale(1)',
      marginLeft: '12px',
    })
  ),
  state(
    'initial-out',
    style({
      width: '0px',
      opacity: 0,
      transform: 'scale(0.95)',
      marginLeft: '0px',
    })
  ),
  // Handle initial state without animation when component is created
  transition('void => initial-in', []),
  transition('void => initial-out', []),
  // Transitions from initial states to animated states
  transition('initial-in => out', [animate('240ms ease-out')]),
  transition('initial-out => in', [animate('240ms ease-out')]),
  // Animate between states
  transition('out => in', [animate('240ms ease-out')]),
  transition('in => out', [animate('240ms ease-out')]),
]);
