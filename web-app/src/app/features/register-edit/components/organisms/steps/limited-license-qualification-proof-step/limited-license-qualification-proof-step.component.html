<div class="flex flex-col justify-between gap-12">
  <fish-limited-license-qualification-proof-box></fish-limited-license-qualification-proof-box>
  <fish-edit-footer (backed)="backButtonClicked.emit()" (continued)="handleContinue()" continueButtonType="secondary" [showBackButton]="true">
    @if (showDeclineButton()) {
      <ng-template fishEditFooterPrimaryActions>
        <fish-button size="l" (clicked)="handleDecline()">
          <fish-icon-delete size="48" icon />
          <span [innerText]="'edit_form.limited_license_qualification_proof.decline_button' | translate"></span>
        </fish-button>
      </ng-template>
    }
  </fish-edit-footer>
</div>
