<div class="flex flex-col justify-between gap-12">
  <fish-limited-license-qualification-proof-box></fish-limited-license-qualification-proof-box>
  <fish-edit-footer
    (backed)="backButtonClicked.emit()"
    (continued)="handleContinue()"
    [continueButtonType]="isDeclineButtonVisible() ? 'secondary' : 'primary'"
    [showBackButton]="true"
  >
    <ng-template fishEditFooterPrimaryActions>
      <div>
        <fish-button size="l" (clicked)="handleDecline()">
          <fish-icon-delete size="48" icon />
          <span [innerText]="'edit_form.limited_license_qualification_proof.decline_button' | translate"></span>
        </fish-button>
      </div>
    </ng-template>
  </fish-edit-footer>
</div>
